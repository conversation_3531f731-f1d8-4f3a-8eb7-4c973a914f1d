package main

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"golang.org/x/crypto/curve25519"
	"gopkg.in/yaml.v3"
)

type Node struct {
	Name     string `yaml:"name"`
	IP       string `yaml:"ip"`
	IPv6     string `yaml:"ipv6"`
	Endpoint string `yaml:"endpoint"`
}

type NodesConfig struct {
	Nodes []Node `yaml:"nodes"`
}

type KeyPair struct {
	Private string
	Public  string
}

func loadNodes() ([]Node, error) {
	data, err := os.ReadFile("nodes.yaml")
	if err != nil {
		return nil, fmt.Errorf("failed to read nodes.yaml: %w", err)
	}

	var config NodesConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse nodes.yaml: %w", err)
	}

	if len(config.Nodes) == 0 {
		return nil, fmt.Errorf("no nodes defined in nodes.yaml")
	}

	return config.Nodes, nil
}

func generateWireGuardKeyPair() (*KeyPair, error) {
	// Generate private key (32 random bytes)
	var privateKey [32]byte
	if _, err := rand.Read(privateKey[:]); err != nil {
		return nil, fmt.Errorf("failed to generate private key: %w", err)
	}

	// Clamp the private key (WireGuard requirement)
	privateKey[0] &= 248
	privateKey[31] &= 127
	privateKey[31] |= 64

	// Generate public key from private key
	var publicKey [32]byte
	curve25519.ScalarBaseMult(&publicKey, &privateKey)

	return &KeyPair{
		Private: base64.StdEncoding.EncodeToString(privateKey[:]),
		Public:  base64.StdEncoding.EncodeToString(publicKey[:]),
	}, nil
}

func generateKeys(nodes []Node) error {
	// Create keys directory
	if err := os.MkdirAll("keys", 0755); err != nil {
		return fmt.Errorf("failed to create keys directory: %w", err)
	}

	for _, node := range nodes {
		nodeDir := filepath.Join("keys", node.Name)
		if err := os.MkdirAll(nodeDir, 0755); err != nil {
			return fmt.Errorf("failed to create directory for node %s: %w", node.Name, err)
		}

		// Check if keys already exist
		privateKeyPath := filepath.Join(nodeDir, "private")
		publicKeyPath := filepath.Join(nodeDir, "public")

		if _, err := os.Stat(privateKeyPath); err == nil {
			fmt.Printf("🔑 Keys for %s already exist, skipping...\n", node.Name)
			continue
		}

		// Special handling for problematic nodes
		if node.Name == "aurora" || node.Name == "dns-master" || node.Name == "slave-uk" {
			fmt.Printf("🔑 Generating NEW keys for %s (console access required)\n", node.Name)
		}

		// Generate new key pair
		keyPair, err := generateWireGuardKeyPair()
		if err != nil {
			return fmt.Errorf("failed to generate keys for node %s: %w", node.Name, err)
		}

		// Write private key
		if err := os.WriteFile(privateKeyPath, []byte(keyPair.Private), 0600); err != nil {
			return fmt.Errorf("failed to write private key for node %s: %w", node.Name, err)
		}

		// Write public key
		if err := os.WriteFile(publicKeyPath, []byte(keyPair.Public), 0644); err != nil {
			return fmt.Errorf("failed to write public key for node %s: %w", node.Name, err)
		}

		fmt.Printf("🔑 Generated keys for %s\n", node.Name)
	}

	return nil
}

func loadNodeKeys(nodes []Node) (map[string]*KeyPair, error) {
	keys := make(map[string]*KeyPair)

	for _, node := range nodes {
		privateKeyPath := filepath.Join("keys", node.Name, "private")
		publicKeyPath := filepath.Join("keys", node.Name, "public")

		privateKeyData, err := os.ReadFile(privateKeyPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read private key for node %s: %w", node.Name, err)
		}

		publicKeyData, err := os.ReadFile(publicKeyPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read public key for node %s: %w", node.Name, err)
		}

		keys[node.Name] = &KeyPair{
			Private: strings.TrimSpace(string(privateKeyData)),
			Public:  strings.TrimSpace(string(publicKeyData)),
		}
	}

	return keys, nil
}

func generateConfigs(nodes []Node) error {
	// Load all keys
	keys, err := loadNodeKeys(nodes)
	if err != nil {
		return fmt.Errorf("failed to load keys: %w", err)
	}

	// Create output directory
	if err := os.MkdirAll("output", 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	for _, node := range nodes {
		if err := generateNodeConfig(node, nodes, keys); err != nil {
			return fmt.Errorf("failed to generate config for node %s: %w", node.Name, err)
		}
		fmt.Printf("📝 Generated config for %s\n", node.Name)
	}

	return nil
}

func generateNodeConfig(currentNode Node, allNodes []Node, keys map[string]*KeyPair) error {
	nodeDir := filepath.Join("output", currentNode.Name)
	if err := os.MkdirAll(nodeDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory for node %s: %w", currentNode.Name, err)
	}

	var config strings.Builder

	// Interface section
	config.WriteString("[Interface]\n")
	config.WriteString(fmt.Sprintf("PrivateKey = %s\n", keys[currentNode.Name].Private))
	config.WriteString(fmt.Sprintf("Address = %s/24, %s/64\n", currentNode.IP, currentNode.IPv6))
	config.WriteString("ListenPort = 51820\n\n")

	// Peer sections
	for _, peer := range allNodes {
		if peer.Name == currentNode.Name {
			continue // Skip self
		}

		config.WriteString("[Peer]\n")
		config.WriteString(fmt.Sprintf("PublicKey = %s\n", keys[peer.Name].Public))
		config.WriteString(fmt.Sprintf("AllowedIPs = %s/32, %s/128\n", peer.IP, peer.IPv6))
		config.WriteString(fmt.Sprintf("Endpoint = %s:51820\n", peer.Endpoint))
		config.WriteString("PersistentKeepalive = 15\n\n")
	}

	configPath := filepath.Join(nodeDir, "tangled0.conf")
	if err := os.WriteFile(configPath, []byte(config.String()), 0600); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}
