digraph TangledMesh {
  rankdir=LR;
  node [shape=box, style=filled, fontname="Arial Bold"];
  edge [color=blue, penwidth=2];
  bgcolor=white;
  label="🕸️ Tangled WireGuard Full-Mesh Network";
  labelloc=top;
  fontsize=16;
  fontname="Arial Bold";

  "aurora" [fillcolor=lightblue, label="aurora\n100.100.10.2\n194.46.58.111"];
  "dns-master" [fillcolor=lightgreen, label="dns-master\n100.100.10.3\n194.31.143.221"];
  "slave-uk" [fillcolor=lightyellow, label="slave-uk\n100.100.10.4\n194.31.143.224"];
  "slave-hk" [fillcolor=lightpink, label="slave-hk\n100.100.10.5\n103.213.4.104"];
  "slave-fremont" [fillcolor=lightcyan, label="slave-fremont\n100.100.10.6\n23.154.8.30"];
  "slave-toronto" [fillcolor=wheat, label="slave-toronto\n100.100.10.7\n23.154.9.17"];
  "mx" [fillcolor=lightblue, label="mx\n100.100.10.8\n49.228.208.127"];
  "slave-swiss" [fillcolor=lightgreen, label="slave-swiss\n100.100.10.9\n193.148.250.230"];

  "aurora" -- "dns-master" [label="51820/udp"];
  "aurora" -- "slave-uk" [label="51820/udp"];
  "aurora" -- "slave-hk" [label="51820/udp"];
  "aurora" -- "slave-fremont" [label="51820/udp"];
  "aurora" -- "slave-toronto" [label="51820/udp"];
  "aurora" -- "mx" [label="51820/udp"];
  "aurora" -- "slave-swiss" [label="51820/udp"];
  "dns-master" -- "slave-uk" [label="51820/udp"];
  "dns-master" -- "slave-hk" [label="51820/udp"];
  "dns-master" -- "slave-fremont" [label="51820/udp"];
  "dns-master" -- "slave-toronto" [label="51820/udp"];
  "dns-master" -- "mx" [label="51820/udp"];
  "dns-master" -- "slave-swiss" [label="51820/udp"];
  "slave-uk" -- "slave-hk" [label="51820/udp"];
  "slave-uk" -- "slave-fremont" [label="51820/udp"];
  "slave-uk" -- "slave-toronto" [label="51820/udp"];
  "slave-uk" -- "mx" [label="51820/udp"];
  "slave-uk" -- "slave-swiss" [label="51820/udp"];
  "slave-hk" -- "slave-fremont" [label="51820/udp"];
  "slave-hk" -- "slave-toronto" [label="51820/udp"];
  "slave-hk" -- "mx" [label="51820/udp"];
  "slave-hk" -- "slave-swiss" [label="51820/udp"];
  "slave-fremont" -- "slave-toronto" [label="51820/udp"];
  "slave-fremont" -- "mx" [label="51820/udp"];
  "slave-fremont" -- "slave-swiss" [label="51820/udp"];
  "slave-toronto" -- "mx" [label="51820/udp"];
  "slave-toronto" -- "slave-swiss" [label="51820/udp"];
  "mx" -- "slave-swiss" [label="51820/udp"];

  subgraph cluster_legend {
    label="Legend";
    style=filled;
    fillcolor=lightgray;
    fontname="Arial";
    legend [shape=plaintext, label=<
      <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
        <TR><TD><B>Connection Type</B></TD><TD><B>Details</B></TD></TR>
        <TR><TD>WireGuard Tunnel</TD><TD>UDP Port 51820</TD></TR>
        <TR><TD>Network</TD><TD>100.100.10.0/24</TD></TR>
        <TR><TD>Keepalive</TD><TD>15 seconds</TD></TR>
      </TABLE>
    >];
  }
}
