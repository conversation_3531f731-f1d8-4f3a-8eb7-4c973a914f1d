digraph TangledMesh {
  rankdir=LR;
  node [shape=box, style=filled, fontname="Arial Bold"];
  edge [color=blue, penwidth=2];
  bgcolor=white;
  label="🕸️ Tangled WireGuard Full-Mesh Network";
  labelloc=top;
  fontsize=16;
  fontname="Arial Bold";

  "aurora" [fillcolor=lightblue, label="aurora\n100.100.10.2\n103.55.20.1"];
  "slave-hk" [fillcolor=lightgreen, label="slave-hk\n100.100.10.5\n210.16.120.129"];
  "mx" [fillcolor=lightyellow, label="mx\n100.100.10.8\nyour-public-ip-here"];

  "aurora" -- "slave-hk" [label="51820/udp"];
  "aurora" -- "mx" [label="51820/udp"];
  "slave-hk" -- "mx" [label="51820/udp"];

  subgraph cluster_legend {
    label="Legend";
    style=filled;
    fillcolor=lightgray;
    fontname="Arial";
    legend [shape=plaintext, label=<
      <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
        <TR><TD><B>Connection Type</B></TD><TD><B>Details</B></TD></TR>
        <TR><TD>WireGuard Tunnel</TD><TD>UDP Port 51820</TD></TR>
        <TR><TD>Network</TD><TD>************/24</TD></TR>
        <TR><TD>Keepalive</TD><TD>15 seconds</TD></TR>
      </TABLE>
    >];
  }
}
