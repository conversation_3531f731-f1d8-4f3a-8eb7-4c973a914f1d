digraph TangledMesh {
  rankdir=LR;
  node [shape=box, style=filled, fontname="Arial Bold"];
  edge [color=blue, penwidth=2];
  bgcolor=white;
  label="🕸️ Tangled WireGuard Full-Mesh Network";
  labelloc=top;
  fontsize=16;
  fontname="Arial Bold";

  "aurora" [fillcolor=lightblue, label="aurora\n100.100.10.2\n103.55.20.1"];
  "dns-master" [fillcolor=lightgreen, label="dns-master\n100.100.10.3\nyour-proxmox-ip-here"];
  "slave-uk" [fillcolor=lightyellow, label="slave-uk\n100.100.10.4\nyour-uk-proxmox-ip-here"];
  "slave-hk" [fillcolor=lightpink, label="slave-hk\n100.100.10.5\n210.16.120.129"];
  "slave-fremont" [fillcolor=lightcyan, label="slave-fremont\n100.100.10.6\nyour-fremont-ip-here"];
  "slave-toronto" [fillcolor=wheat, label="slave-toronto\n100.100.10.7\nyour-toronto-ip-here"];
  "mx" [fillcolor=lightblue, label="mx\n100.100.10.8\nyour-macbook-public-ip-here"];
  "slave-swiss" [fillcolor=lightgreen, label="slave-swiss\n100.100.10.9\nyour-swiss-ip-here"];

  "aurora" -- "dns-master" [label="51820/udp"];
  "aurora" -- "slave-uk" [label="51820/udp"];
  "aurora" -- "slave-hk" [label="51820/udp"];
  "aurora" -- "slave-fremont" [label="51820/udp"];
  "aurora" -- "slave-toronto" [label="51820/udp"];
  "aurora" -- "mx" [label="51820/udp"];
  "aurora" -- "slave-swiss" [label="51820/udp"];
  "dns-master" -- "slave-uk" [label="51820/udp"];
  "dns-master" -- "slave-hk" [label="51820/udp"];
  "dns-master" -- "slave-fremont" [label="51820/udp"];
  "dns-master" -- "slave-toronto" [label="51820/udp"];
  "dns-master" -- "mx" [label="51820/udp"];
  "dns-master" -- "slave-swiss" [label="51820/udp"];
  "slave-uk" -- "slave-hk" [label="51820/udp"];
  "slave-uk" -- "slave-fremont" [label="51820/udp"];
  "slave-uk" -- "slave-toronto" [label="51820/udp"];
  "slave-uk" -- "mx" [label="51820/udp"];
  "slave-uk" -- "slave-swiss" [label="51820/udp"];
  "slave-hk" -- "slave-fremont" [label="51820/udp"];
  "slave-hk" -- "slave-toronto" [label="51820/udp"];
  "slave-hk" -- "mx" [label="51820/udp"];
  "slave-hk" -- "slave-swiss" [label="51820/udp"];
  "slave-fremont" -- "slave-toronto" [label="51820/udp"];
  "slave-fremont" -- "mx" [label="51820/udp"];
  "slave-fremont" -- "slave-swiss" [label="51820/udp"];
  "slave-toronto" -- "mx" [label="51820/udp"];
  "slave-toronto" -- "slave-swiss" [label="51820/udp"];
  "mx" -- "slave-swiss" [label="51820/udp"];

  subgraph cluster_legend {
    label="Legend";
    style=filled;
    fillcolor=lightgray;
    fontname="Arial";
    legend [shape=plaintext, label=<
      <TABLE BORDER="0" CELLBORDER="1" CELLSPACING="0">
        <TR><TD><B>Connection Type</B></TD><TD><B>Details</B></TD></TR>
        <TR><TD>WireGuard Tunnel</TD><TD>UDP Port 51820</TD></TR>
        <TR><TD>Network</TD><TD>100.100.10.0/24</TD></TR>
        <TR><TD>Keepalive</TD><TD>15 seconds</TD></TR>
      </TABLE>
    >];
  }
}
