package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
)

func deployNode(nodeName, sshTarget string) error {
	// Verify config exists
	configPath := filepath.Join("output", nodeName, "tangled0.conf")
	if _, err := os.Stat(configPath); err != nil {
		return fmt.Errorf("config file not found: %s (run 'tangled generate' first)", configPath)
	}

	fmt.Printf("📋 Checking WireGuard installation on %s...\n", sshTarget)

	// Check if WireGuard is installed, install if not
	if err := ensureWireGuardInstalled(sshTarget); err != nil {
		return fmt.Errorf("failed to ensure WireGuard installation: %w", err)
	}

	fmt.Printf("📤 Uploading configuration to %s...\n", sshTarget)

	// Upload configuration
	if err := uploadConfig(configPath, sshTarget); err != nil {
		return fmt.Errorf("failed to upload config: %w", err)
	}

	fmt.Printf("🔄 Managing WireGuard service on %s...\n", sshTarget)

	// Handle existing WireGuard interface and start new one
	if err := manageWireGuardService(sshTarget); err != nil {
		return fmt.Errorf("failed to manage WireGuard service: %w", err)
	}

	fmt.Printf("✅ WireGuard interface tangled0 is active on %s\n", sshTarget)
	return nil
}

func ensureWireGuardInstalled(sshTarget string) error {
	// Check if WireGuard is installed
	checkCmd := exec.Command("ssh", sshTarget, "which wg-quick")
	if err := checkCmd.Run(); err == nil {
		fmt.Printf("✅ WireGuard already installed on %s\n", sshTarget)
		return nil
	}

	fmt.Printf("📦 Installing WireGuard on %s...\n", sshTarget)

	// Detect OS and install WireGuard
	installScript := `
		set -e
		if command -v apt-get >/dev/null 2>&1; then
			# Debian/Ubuntu
			apt-get update -qq
			apt-get install -y wireguard
		elif command -v yum >/dev/null 2>&1; then
			# RHEL/CentOS
			yum install -y epel-release
			yum install -y wireguard-tools
		elif command -v dnf >/dev/null 2>&1; then
			# Fedora
			dnf install -y wireguard-tools
		elif command -v pacman >/dev/null 2>&1; then
			# Arch Linux
			pacman -S --noconfirm wireguard-tools
		elif command -v apk >/dev/null 2>&1; then
			# Alpine Linux
			apk add --no-cache wireguard-tools
		else
			echo "❌ Unsupported package manager. Please install WireGuard manually."
			exit 1
		fi
		echo "✅ WireGuard installation completed"
	`

	installCmd := exec.Command("ssh", sshTarget, "bash", "-c", installScript)
	installCmd.Stdout = os.Stdout
	installCmd.Stderr = os.Stderr

	if err := installCmd.Run(); err != nil {
		return fmt.Errorf("WireGuard installation failed: %w", err)
	}

	return nil
}

func uploadConfig(configPath, sshTarget string) error {
	// Create WireGuard directory if it doesn't exist
	mkdirCmd := exec.Command("ssh", sshTarget, "mkdir", "-p", "/etc/wireguard")
	if err := mkdirCmd.Run(); err != nil {
		return fmt.Errorf("failed to create /etc/wireguard directory: %w", err)
	}

	// Upload the configuration file
	scpCmd := exec.Command("scp", configPath, sshTarget+":/etc/wireguard/tangled0.conf")
	scpCmd.Stdout = os.Stdout
	scpCmd.Stderr = os.Stderr

	if err := scpCmd.Run(); err != nil {
		return fmt.Errorf("failed to upload config via SCP: %w", err)
	}

	// Set proper permissions
	chmodCmd := exec.Command("ssh", sshTarget, "chmod", "600", "/etc/wireguard/tangled0.conf")
	if err := chmodCmd.Run(); err != nil {
		return fmt.Errorf("failed to set config permissions: %w", err)
	}

	return nil
}

func manageWireGuardService(sshTarget string) error {
	// Script to handle existing WireGuard interfaces and start tangled0
	managementScript := `
		set -e
		
		# Check if tangled0 is already running
		if wg show tangled0 >/dev/null 2>&1; then
			echo "🔄 Stopping existing tangled0 interface..."
			wg-quick down tangled0 || true
		fi
		
		# Check for other WireGuard interfaces that might conflict
		existing_interfaces=$(wg show interfaces 2>/dev/null | tr ' ' '\n' | grep -v '^$' | grep -v '^tangled0$' || true)
		
		if [ ! -z "$existing_interfaces" ]; then
			echo "⚠️  Found existing WireGuard interfaces: $existing_interfaces"
			echo "🔄 Stopping conflicting interfaces..."
			for iface in $existing_interfaces; do
				echo "   Stopping $iface..."
				wg-quick down "$iface" || true
			done
		fi
		
		# Start tangled0
		echo "🚀 Starting tangled0 interface..."
		wg-quick up tangled0
		
		# Verify it's running
		if wg show tangled0 >/dev/null 2>&1; then
			echo "✅ tangled0 interface is active"
			wg show tangled0
		else
			echo "❌ Failed to start tangled0 interface"
			exit 1
		fi
		
		# Enable systemd service if available
		if systemctl --version >/dev/null 2>&1; then
			echo "🔧 Enabling systemd service..."
			systemctl enable wg-quick@tangled0 || true
		fi
	`

	manageCmd := exec.Command("ssh", sshTarget, "bash", "-c", managementScript)
	manageCmd.Stdout = os.Stdout
	manageCmd.Stderr = os.Stderr

	if err := manageCmd.Run(); err != nil {
		return fmt.Errorf("failed to manage WireGuard service: %w", err)
	}

	return nil
}

func updateNodeConfig(nodeName, sshTarget string) error {
	// Verify config exists
	configPath := filepath.Join("output", nodeName, "tangled0.conf")
	if _, err := os.Stat(configPath); err != nil {
		return fmt.Errorf("config file not found: %s (run 'tangled generate' first)", configPath)
	}

	fmt.Printf("📤 Uploading updated configuration to %s...\n", sshTarget)

	// Upload the updated configuration
	if err := uploadConfig(configPath, sshTarget); err != nil {
		return fmt.Errorf("failed to upload config: %w", err)
	}

	fmt.Printf("🔄 Restarting WireGuard service on %s...\n", sshTarget)

	// Restart WireGuard service with updated config
	if err := restartWireGuardService(sshTarget); err != nil {
		return fmt.Errorf("failed to restart WireGuard service: %w", err)
	}

	return nil
}

func restartWireGuardService(sshTarget string) error {
	// Script to gracefully restart WireGuard with new config
	restartScript := `
		set -e

		echo "🔄 Restarting WireGuard with updated configuration..."

		# Stop current interface
		if wg show tangled0 >/dev/null 2>&1; then
			echo "   Stopping tangled0..."
			wg-quick down tangled0
		fi

		# Start with new config
		echo "   Starting tangled0 with updated config..."
		wg-quick up tangled0

		# Verify it's running
		if wg show tangled0 >/dev/null 2>&1; then
			echo "✅ tangled0 interface restarted successfully"
			wg show tangled0 | head -5
		else
			echo "❌ Failed to restart tangled0 interface"
			exit 1
		fi
	`

	restartCmd := exec.Command("ssh", sshTarget, "bash", "-c", restartScript)
	restartCmd.Stdout = os.Stdout
	restartCmd.Stderr = os.Stderr

	if err := restartCmd.Run(); err != nil {
		return fmt.Errorf("failed to restart WireGuard service: %w", err)
	}

	return nil
}
