# Tangled Node Configuration
# IPv4 WireGuard subnet: ************/24
# IPv6 WireGuard subnet: fd00:100:100:10::/64

nodes:
  - name: aurora
    ip: ************
    ipv6: fd00:100:100:10::2
    endpoint: ************

  - name: dns-master
    ip: ************
    ipv6: fd00:100:100:10::3
    endpoint: **************

  - name: slave-uk
    ip: ************
    ipv6: fd00:100:100:10::4
    endpoint: **************

  - name: slave-hk
    ip: ************
    ipv6: fd00:100:100:10::5
    endpoint: *************

  - name: slave-fremont
    ip: ************
    ipv6: fd00:100:100:10::6
    endpoint: ***********

  - name: slave-toronto
    ip: ************
    ipv6: fd00:100:100:10::7
    endpoint: ***********

  - name: mx
    ip: ************
    ipv6: fd00:100:100:10::8
    endpoint: **************

  - name: slave-swiss
    ip: ************
    ipv6: fd00:100:100:10::9
    endpoint: ***************
