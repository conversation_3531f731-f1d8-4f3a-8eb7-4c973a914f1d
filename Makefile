.PHONY: build clean install test generate deploy visualize

# Build the binary
build:
	go build -o tangled

# Build for multiple platforms
build-all:
	GOOS=linux GOARCH=amd64 go build -o tangled-linux-amd64
	GOOS=darwin GOARCH=amd64 go build -o tangled-darwin-amd64
	GOOS=darwin GOARCH=arm64 go build -o tangled-darwin-arm64
	GOOS=windows GOARCH=amd64 go build -o tangled-windows-amd64.exe

# Clean build artifacts
clean:
	rm -f tangled tangled-*
	rm -rf keys/ output/ graphs/

# Install dependencies
deps:
	go mod tidy
	go mod download

# Run tests (placeholder for future tests)
test:
	go test ./...

# Quick commands
generate: build
	./tangled generate

visualize: build
	./tangled visualize

# Show help
help: build
	./tangled help

# Development workflow
dev: clean deps build generate visualize

# Install to system (optional)
install: build
	sudo cp tangled /usr/local/bin/

.DEFAULT_GOAL := build
