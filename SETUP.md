# 🚀 Tangled Setup Guide

## Your Infrastructure Overview

**8-Node Full-Mesh WireGuard Network:**

| Node | IP | Role | Provider | Notes |
|------|----|----- |----------|-------|
| `aurora` | ************ | Main BGP | Packet/Equinix | Peering with Packet Star |
| `dns-master` | ************ | DNS Hidden Master | Proxmox | Non-BGP |
| `slave-uk` | ************ | DNS Slave | Proxmox | Non-BGP, Anycast DNS |
| `slave-hk` | ************ | BGP Slave | Skywolf | BGP enabled |
| `slave-fremont` | ************ | BGP Slave | Paradox | BGP enabled |
| `slave-toronto` | ************ | BGP Slave | Paradox | BGP enabled |
| `mx` | ************ | Your MacBook | Local | Development access |
| `slave-swiss` | ************ | BGP Slave | iFog | BGP enabled |

## 📋 Pre-Deployment Checklist

### 1. Update `nodes.yaml`
Replace all placeholder IPs with your actual public IP addresses:

```yaml
# Before
endpoint: YOUR_AURORA_PUBLIC_IP

# After  
endpoint: ***********  # (your actual IP)
```

### 2. Verify SSH Access
Ensure you can SSH to all nodes as root with key-based authentication:

```bash
ssh root@YOUR_AURORA_PUBLIC_IP
ssh root@YOUR_DNS_MASTER_PUBLIC_IP
ssh root@YOUR_HK_SLAVE_PUBLIC_IP
# ... etc for all nodes
```

### 3. Firewall Preparation
Make sure UDP port 51820 is open on all nodes. Tangled won't modify your firewall rules.

## 🔥 Deployment Workflow

### Step 1: Generate Everything
```bash
make build
./tangled generate
```

This creates:
- `keys/` - Unique key pairs for each node
- `output/` - Complete WireGuard configs with full-mesh peering

### Step 2: Deploy to All Nodes
```bash
./tangled deploy aurora root@YOUR_AURORA_PUBLIC_IP
./tangled deploy dns-master root@YOUR_DNS_MASTER_PUBLIC_IP
./tangled deploy slave-uk root@YOUR_UK_SLAVE_PUBLIC_IP
./tangled deploy slave-hk root@YOUR_HK_SLAVE_PUBLIC_IP
./tangled deploy slave-fremont root@YOUR_FREMONT_SLAVE_PUBLIC_IP
./tangled deploy slave-toronto root@YOUR_TORONTO_SLAVE_PUBLIC_IP
./tangled deploy slave-swiss root@YOUR_SWISS_SLAVE_PUBLIC_IP

# Skip 'mx' - that's your MacBook, handle separately if needed
```

### Step 3: Verify Connectivity
From any node, test the mesh:

```bash
# From aurora, ping other nodes
ping ************  # dns-master
ping ************  # slave-hk
ping ************  # slave-swiss
```

### Step 4: Visualize Your Network
```bash
./tangled visualize
# Creates graphs/topology.dot - render with Graphviz if available
```

## 🛠️ What Tangled Does Automatically

For each node deployment:
- ✅ **Detects OS** and installs WireGuard (apt/yum/dnf/pacman)
- ✅ **Stops conflicting** WireGuard interfaces
- ✅ **Uploads config** to `/etc/wireguard/tangled0.conf`
- ✅ **Starts interface** with `wg-quick up tangled0`
- ✅ **Enables systemd** service for persistence

## 🔒 Security Notes

- Private keys stored locally in `keys/` directory
- WireGuard configs deployed with 600 permissions
- Existing WireGuard interfaces replaced with authority
- No firewall or routing modifications

## 🚨 Troubleshooting

### If deployment fails:
```bash
# Check WireGuard status on target node
ssh root@NODE_IP "wg show"
ssh root@NODE_IP "systemctl status wg-quick@tangled0"
```

### If connectivity fails:
```bash
# Check if port 51820 is open
ssh root@NODE_IP "ss -ulnp | grep 51820"

# Check WireGuard logs
ssh root@NODE_IP "journalctl -u wg-quick@tangled0 -f"
```

## 🎯 Post-Deployment

Your BGP/Anycast infrastructure now has a secure WireGuard overlay network!

- **DNS queries** can flow securely between master and slaves
- **BGP sessions** can use the WireGuard tunnel if needed
- **Management traffic** flows over encrypted channels
- **Your MacBook** has secure access to the entire infrastructure

---

**Ready to deploy? Update those IP addresses and ship it! 🚢**
