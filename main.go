package main

import (
	"fmt"
	"os"
)

const (
	version = "v0.1.0"
	banner  = `
🕸️  Tangled %s - WireGuard Full-Mesh Generator
    Minimal. Powerful. No Bullshit.
`
)

func main() {
	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}

	command := os.Args[1]

	switch command {
	case "generate":
		handleGenerate()
	case "deploy":
		handleDeploy()
	case "visualize":
		handleVisualize()
	case "version":
		fmt.Printf("Tangled %s\n", version)
	case "help", "-h", "--help":
		printUsage()
	default:
		fmt.Printf("Unknown command: %s\n\n", command)
		printUsage()
		os.Exit(1)
	}
}

func printUsage() {
	fmt.Printf(banner, version)
	fmt.Println(`
Commands:
  generate              Generate WireGuard keys and configs for all nodes
  deploy <node> <host>  Deploy config to specific node via SSH
  visualize             Generate Graphviz network topology
  version               Show version information
  help                  Show this help message

Examples:
  tangled generate
  tangled deploy aurora root@***********
  tangled visualize
`)
}

func handleGenerate() {
	fmt.Println("🔑 Generating WireGuard keys and configurations...")

	nodes, err := loadNodes()
	if err != nil {
		fmt.Printf("❌ Error loading nodes: %v\n", err)
		os.Exit(1)
	}

	if err := generateKeys(nodes); err != nil {
		fmt.Printf("❌ Error generating keys: %v\n", err)
		os.Exit(1)
	}

	if err := generateConfigs(nodes); err != nil {
		fmt.Printf("❌ Error generating configs: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Generated configurations for %d nodes\n", len(nodes))
	fmt.Println("📁 Keys saved to: keys/")
	fmt.Println("📁 Configs saved to: output/")
}

func handleDeploy() {
	if len(os.Args) < 4 {
		fmt.Println("❌ Usage: tangled deploy <node> <ssh_target>")
		fmt.Println("   Example: tangled deploy aurora root@***********")
		os.Exit(1)
	}

	nodeName := os.Args[2]
	sshTarget := os.Args[3]

	fmt.Printf("🚀 Deploying %s to %s...\n", nodeName, sshTarget)

	if err := deployNode(nodeName, sshTarget); err != nil {
		fmt.Printf("❌ Deployment failed: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Successfully deployed %s\n", nodeName)
}

func handleVisualize() {
	fmt.Println("📊 Generating network topology visualization...")

	nodes, err := loadNodes()
	if err != nil {
		fmt.Printf("❌ Error loading nodes: %v\n", err)
		os.Exit(1)
	}

	if err := generateVisualization(nodes); err != nil {
		fmt.Printf("❌ Error generating visualization: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ Visualization generated: graphs/topology.dot")
	fmt.Println("💡 Render with: dot -Tpng graphs/topology.dot -o topology.png")
}
