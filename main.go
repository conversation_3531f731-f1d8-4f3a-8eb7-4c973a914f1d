package main

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
)

const (
	version = "v0.1.0"
	banner  = `
🕸️  Tangled %s - WireGuard Full-Mesh Generator
    Minimal. Powerful. No Bullshit.
`
)

func main() {
	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}

	command := os.Args[1]

	switch command {
	case "generate":
		handleGenerate()
	case "deploy":
		handleDeploy()
	case "deploy-all":
		handleDeployAll()
	case "setup-local":
		handleSetupLocal()
	case "visualize":
		handleVisualize()
	case "version":
		fmt.Printf("Tangled %s\n", version)
	case "help", "-h", "--help":
		printUsage()
	default:
		fmt.Printf("Unknown command: %s\n\n", command)
		printUsage()
		os.Exit(1)
	}
}

func printUsage() {
	fmt.Printf(banner, version)
	fmt.Println(`
Commands:
  generate              Generate WireGuard keys and configs for all nodes
  deploy <node> <host>  Deploy config to specific node via SSH
  deploy-all            Deploy configs to ALL nodes automatically
  setup-local           Setup WireGuard on local MacBook
  visualize             Generate Graphviz network topology
  version               Show version information
  help                  Show this help message

Examples:
  tangled generate
  tangled deploy aurora root@YOUR_AURORA_PUBLIC_IP
  tangled deploy-all
  tangled setup-local
  tangled visualize
`)
}

func handleGenerate() {
	fmt.Println("🔑 Generating WireGuard keys and configurations...")

	nodes, err := loadNodes()
	if err != nil {
		fmt.Printf("❌ Error loading nodes: %v\n", err)
		os.Exit(1)
	}

	if err := generateKeys(nodes); err != nil {
		fmt.Printf("❌ Error generating keys: %v\n", err)
		os.Exit(1)
	}

	if err := generateConfigs(nodes); err != nil {
		fmt.Printf("❌ Error generating configs: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Generated configurations for %d nodes\n", len(nodes))
	fmt.Println("📁 Keys saved to: keys/")
	fmt.Println("📁 Configs saved to: output/")
}

func handleDeploy() {
	if len(os.Args) < 4 {
		fmt.Println("❌ Usage: tangled deploy <node> <ssh_target>")
		fmt.Println("   Example: tangled deploy aurora root@YOUR_AURORA_PUBLIC_IP")
		os.Exit(1)
	}

	nodeName := os.Args[2]
	sshTarget := os.Args[3]

	fmt.Printf("🚀 Deploying %s to %s...\n", nodeName, sshTarget)

	if err := deployNode(nodeName, sshTarget); err != nil {
		fmt.Printf("❌ Deployment failed: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ Successfully deployed %s\n", nodeName)
}

func handleDeployAll() {
	fmt.Println("🚀 Deploying to ALL nodes...")

	nodes, err := loadNodes()
	if err != nil {
		fmt.Printf("❌ Error loading nodes: %v\n", err)
		os.Exit(1)
	}

	var failed []string
	var skipped []string
	var deployed []string

	for i, node := range nodes {
		// Handle MacBook deployment differently
		if node.Name == "mx" {
			fmt.Printf("\n[%d/%d] 💻 Setting up %s (MacBook)...\n", i+1, len(nodes), node.Name)

			// For MacBook, just copy the config locally
			configPath := fmt.Sprintf("output/%s/tangled0.conf", node.Name)
			if _, err := os.Stat(configPath); err != nil {
				fmt.Printf("❌ Config not found for %s: %s\n", node.Name, configPath)
				failed = append(failed, node.Name)
				continue
			}

			fmt.Printf("📋 MacBook config ready at: %s\n", configPath)
			fmt.Println("💡 To activate on your MacBook:")
			fmt.Println("   sudo cp output/mx/tangled0.conf /etc/wireguard/")
			fmt.Println("   sudo wg-quick up tangled0")

			deployed = append(deployed, node.Name)
			continue
		}

		sshTarget := fmt.Sprintf("root@%s", node.Endpoint)
		fmt.Printf("\n[%d/%d] 🎯 Deploying %s to %s...\n", i+1, len(nodes), node.Name, sshTarget)

		if err := deployNode(node.Name, sshTarget); err != nil {
			fmt.Printf("❌ Failed to deploy %s: %v\n", node.Name, err)
			failed = append(failed, node.Name)

			// Ask if user wants to continue
			fmt.Print("Continue with remaining deployments? (y/N): ")
			var response string
			fmt.Scanln(&response)
			if response != "y" && response != "Y" {
				fmt.Println("🛑 Deployment stopped by user")
				break
			}
		} else {
			fmt.Printf("✅ Successfully deployed %s\n", node.Name)
			deployed = append(deployed, node.Name)
		}
	}

	// Summary
	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Println("📊 DEPLOYMENT SUMMARY")
	fmt.Println(strings.Repeat("=", 50))

	if len(deployed) > 0 {
		fmt.Printf("✅ Successfully deployed (%d): %v\n", len(deployed), deployed)
	}

	if len(skipped) > 0 {
		fmt.Printf("⏭️  Skipped (%d): %v\n", len(skipped), skipped)
	}

	if len(failed) > 0 {
		fmt.Printf("❌ Failed (%d): %v\n", len(failed), failed)
		os.Exit(1)
	}

	fmt.Println("🎉 All deployments completed successfully!")
}

func handleSetupLocal() {
	fmt.Println("💻 Setting up WireGuard on local MacBook...")

	// Find the mx node config
	configPath := "output/mx/tangled0.conf"
	if _, err := os.Stat(configPath); err != nil {
		fmt.Printf("❌ MacBook config not found: %s\n", configPath)
		fmt.Println("💡 Run 'tangled generate' first to create configs")
		os.Exit(1)
	}

	fmt.Println("📋 Installing WireGuard config on MacBook...")

	// Check if WireGuard is installed
	fmt.Println("🔍 Checking WireGuard installation...")
	if err := exec.Command("which", "wg-quick").Run(); err != nil {
		fmt.Println("❌ WireGuard not found. Install it first:")
		fmt.Println("   brew install wireguard-tools")
		os.Exit(1)
	}

	// Copy config to system location
	fmt.Println("📤 Installing config to /etc/wireguard/...")
	copyCmd := exec.Command("sudo", "cp", configPath, "/etc/wireguard/tangled0.conf")
	if err := copyCmd.Run(); err != nil {
		fmt.Printf("❌ Failed to copy config: %v\n", err)
		fmt.Println("💡 Try manually: sudo cp output/mx/tangled0.conf /etc/wireguard/")
		os.Exit(1)
	}

	// Set permissions
	chmodCmd := exec.Command("sudo", "chmod", "600", "/etc/wireguard/tangled0.conf")
	if err := chmodCmd.Run(); err != nil {
		fmt.Printf("⚠️  Warning: Failed to set permissions: %v\n", err)
	}

	// Stop any existing WireGuard interfaces
	fmt.Println("🔄 Managing existing WireGuard interfaces...")
	downCmd := exec.Command("sudo", "wg-quick", "down", "tangled0")
	downCmd.Run() // Ignore errors if interface doesn't exist

	// Start the interface
	fmt.Println("🚀 Starting tangled0 interface...")
	upCmd := exec.Command("sudo", "wg-quick", "up", "tangled0")
	upCmd.Stdout = os.Stdout
	upCmd.Stderr = os.Stderr

	if err := upCmd.Run(); err != nil {
		fmt.Printf("❌ Failed to start interface: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ MacBook successfully connected to WireGuard mesh!")
	fmt.Println("💡 Test connectivity: ping 100.100.10.2  # (aurora)")
}

func handleVisualize() {
	fmt.Println("📊 Generating network topology visualization...")

	nodes, err := loadNodes()
	if err != nil {
		fmt.Printf("❌ Error loading nodes: %v\n", err)
		os.Exit(1)
	}

	if err := generateVisualization(nodes); err != nil {
		fmt.Printf("❌ Error generating visualization: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("✅ Visualization generated: graphs/topology.dot")
	fmt.Println("💡 Render with: dot -Tpng graphs/topology.dot -o topology.png")
}
