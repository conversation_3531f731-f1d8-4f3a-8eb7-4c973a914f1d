# 🕸️ Tangled v0.1.0

**WireGuard Full-Mesh Generator**  
*Minimal. Powerful. No Bullshit.*

Tangled is a Go-based tool designed to automate the generation and deployment of WireGuard full-mesh VPN configurations across a fleet of nodes, tailored for BGP/Anycast infrastructure with zero configuration pollution.

## ✨ Features

- 🔑 **Automatic Key Generation** - Generates WireGuard private/public key pairs for each node
- 📝 **Full-Mesh Configuration** - Creates complete `tangled0.conf` files with all peer connections
- 🚀 **One-Command Deployment** - SSH-based deployment with automatic WireGuard installation
- 🧠 **Smart Conflict Resolution** - Handles existing WireGuard interfaces with authority
- 📊 **Network Visualization** - Generates Graphviz topology diagrams
- 🏗️ **Single Binary** - Compiles to a single executable with no dependencies

## 🚀 Quick Start

### 1. Configure Your Nodes

Edit `nodes.yaml` with your infrastructure:

```yaml
nodes:
  - name: aurora
    ip: ************
    endpoint: ***********
  - name: slave-hk
    ip: ************
    endpoint: **************
  - name: mx
    ip: ************
    endpoint: your-public-ip-here
```

### 2. Generate Configurations

```bash
./tangled generate
```

This creates:
- `keys/` - Private/public key pairs for each node
- `output/` - Complete `tangled0.conf` files ready for deployment

### 3. Deploy to Nodes

```bash
./tangled deploy aurora root@***********
./tangled deploy slave-hk root@**************
./tangled deploy mx root@your-public-ip-here
```

### 4. Visualize Your Network

```bash
./tangled visualize
```

Generates `graphs/topology.dot` - render with Graphviz:
```bash
dot -Tpng graphs/topology.dot -o topology.png
```

## 📋 Commands

| Command | Description |
|---------|-------------|
| `generate` | Generate WireGuard keys and configs for all nodes |
| `deploy <node> <host>` | Deploy config to specific node via SSH |
| `visualize` | Generate Graphviz network topology |
| `version` | Show version information |
| `help` | Show help message |

## 🔧 Configuration Details

### Network Settings
- **Subnet**: `************/24`
- **Port**: `51820/udp`
- **Keepalive**: `15 seconds`
- **AllowedIPs**: `************/24, ************/32`

### Deployment Behavior
- Automatically installs WireGuard if not present
- Stops conflicting WireGuard interfaces
- Replaces existing `tangled0` configurations
- Enables systemd service for persistence
- Requires root SSH access (key-based authentication)

## 🏗️ Building

```bash
go build -o tangled
```

## 📁 Directory Structure

```
tangled/
├── main.go              # CLI entrypoint
├── generator.go         # Key and config generation
├── deploy.go           # SSH deployment logic
├── visualizer.go       # Graphviz generation
├── nodes.yaml          # Node configuration
├── keys/               # Generated private/public keys
│   └── <node>/
├── output/             # Generated WireGuard configs
│   └── <node>/tangled0.conf
└── graphs/             # Network visualizations
    └── topology.dot
```

## ⚠️ Design Philosophy

**What Tangled Does:**
- ✅ Generates WireGuard configurations
- ✅ Manages WireGuard keys securely
- ✅ Deploys via SSH with smart conflict resolution
- ✅ Provides network visualization

**What Tangled Doesn't Do:**
- ❌ No firewall modifications
- ❌ No routing table changes
- ❌ No system service management beyond WireGuard
- ❌ No configuration templating languages
- ❌ No persistent agents or daemons

## 🔒 Security Notes

- Private keys are stored locally in `keys/` directory
- SSH deployment requires root access with key-based authentication
- WireGuard configs are deployed with `600` permissions
- Existing WireGuard interfaces are replaced with authority

## 📊 Example Output

```
🕸️  Tangled v0.1.0 - WireGuard Full-Mesh Generator
    Minimal. Powerful. No Bullshit.

🔑 Generating WireGuard keys and configurations...
🔑 Generated keys for aurora
🔑 Generated keys for slave-hk
🔑 Generated keys for mx
📝 Generated config for aurora
📝 Generated config for slave-hk
📝 Generated config for mx
✅ Generated configurations for 3 nodes
```

---

**Built for infrastructure engineers who value simplicity, control, and reliability.**
