#!/bin/bash

# Tangled Example Workflow
# This script demonstrates the complete Tangled workflow

set -e

echo "🕸️ Tangled v0.1.0 - Example Workflow"
echo "======================================"
echo

# Step 1: Build Tangled
echo "🔨 Building Tangled..."
make build
echo "✅ Build complete"
echo

# Step 2: Show help
echo "📖 Tangled Help:"
./tangled help
echo

# Step 3: Generate configurations
echo "🔑 Generating WireGuard configurations..."
./tangled generate
echo

# Step 4: Show generated files
echo "📁 Generated Files:"
echo "Keys:"
find keys -type f | head -6
echo
echo "Configs:"
find output -name "*.conf"
echo

# Step 5: Show sample configuration
echo "📝 Sample Configuration (aurora):"
echo "=================================="
cat output/aurora/tangled0.conf
echo

# Step 6: Generate visualization
echo "📊 Generating network visualization..."
./tangled visualize
echo

# Step 7: Show deployment examples
echo "🚀 Deployment Examples:"
echo "======================="
echo "To deploy to your nodes, run:"
echo "  ./tangled deploy aurora root@***********"
echo "  ./tangled deploy slave-hk root@**************"
echo "  ./tangled deploy mx root@your-public-ip-here"
echo

echo "✅ Example workflow complete!"
echo
echo "💡 Next steps:"
echo "  1. Update nodes.yaml with your actual IP addresses"
echo "  2. Run './tangled generate' to regenerate configs"
echo "  3. Deploy to your nodes using './tangled deploy <node> <ssh_target>'"
echo "  4. Verify connectivity between nodes"
