#!/bin/bash

# Tangled Example Workflow
# This script demonstrates the complete Tangled workflow

set -e

echo "🕸️ Tangled v0.1.0 - Example Workflow"
echo "======================================"
echo

# Step 1: Build Tangled
echo "🔨 Building Tangled..."
make build
echo "✅ Build complete"
echo

# Step 2: Show help
echo "📖 Tangled Help:"
./tangled help
echo

# Step 3: Generate configurations
echo "🔑 Generating WireGuard configurations..."
./tangled generate
echo

# Step 4: Show generated files
echo "📁 Generated Files:"
echo "Keys:"
find keys -type f | head -6
echo
echo "Configs:"
find output -name "*.conf"
echo

# Step 5: Show sample configuration
echo "📝 Sample Configuration (aurora):"
echo "=================================="
cat output/aurora/tangled0.conf
echo

# Step 6: Generate visualization
echo "📊 Generating network visualization..."
./tangled visualize
echo

# Step 7: Show deployment examples
echo "🚀 Deployment Examples:"
echo "======================="
echo "To deploy to your nodes, run:"
echo "  ./tangled deploy aurora root@YOUR_AURORA_PUBLIC_IP"
echo "  ./tangled deploy dns-master root@YOUR_DNS_MASTER_PUBLIC_IP"
echo "  ./tangled deploy slave-hk root@YOUR_HK_SLAVE_PUBLIC_IP"
echo "  ./tangled deploy slave-fremont root@YOUR_FREMONT_SLAVE_PUBLIC_IP"
echo "  ./tangled deploy slave-toronto root@YOUR_TORONTO_SLAVE_PUBLIC_IP"
echo "  ./tangled deploy slave-swiss root@YOUR_SWISS_SLAVE_PUBLIC_IP"
echo "  # Note: Skip 'mx' deployment as it's your local MacBook"
echo

echo "✅ Example workflow complete!"
echo
echo "💡 Next steps:"
echo "  1. Update nodes.yaml with your actual public IP addresses"
echo "  2. Run './tangled generate' to create keys and configs"
echo "  3. Deploy to your nodes using './tangled deploy <node> root@<public_ip>'"
echo "  4. Verify full-mesh connectivity between all nodes"
echo "  5. Your BGP/Anycast infrastructure will now have secure WireGuard overlay!"
