MacBook-Pro:tangled marcxavier$ ./tangled setup-local
💻 Setting up WireGuard on local MacBook...
❌ MacBook config not found: output/mx/tangled0.conf
💡 Run 'tangled generate' first to create configs
MacBook-Pro:tangled marcxavier$ make clean
rm -f tangled tangled-*
rm -rf keys/ output/ graphs/
MacBook-Pro:tangled marcxavier$ 
MacBook-Pro:tangled marcxavier$ make clean && make build
rm -f tangled tangled-*
rm -rf keys/ output/ graphs/
go build -o tangled
MacBook-Pro:tangled marcxavier$ 
MacBook-Pro:tangled marcxavier$ ./tangled generate
🔑 Generating WireGuard keys and configurations...
🔑 Generated keys for aurora
🔑 Generated keys for dns-master
🔑 Generated keys for slave-uk
🔑 Generated keys for slave-hk
🔑 Generated keys for slave-fremont
🔑 Generated keys for slave-toronto
🔑 Generated keys for mx
🔑 Generated keys for slave-swiss
📝 Generated config for aurora
📝 Generated config for dns-master
📝 Generated config for slave-uk
📝 Generated config for slave-hk
📝 Generated config for slave-fremont
📝 Generated config for slave-toronto
📝 Generated config for mx
📝 Generated config for slave-swiss
✅ Generated configurations for 8 nodes
📁 Keys saved to: keys/
📁 Configs saved to: output/
MacBook-Pro:tangled marcxavier$ ./tangled deploy-all
🚀 Deploying to ALL nodes...

[1/8] 🎯 Deploying aurora to root@*************...
📋 Checking WireGuard installation on root@*************...
✅ WireGuard already installed on root@*************
📤 Uploading configuration to root@*************...
Warning: Permanently added '*************' (ED25519) to the list of known hosts.
tangled0.conf            100% 1167     5.8KB/s   00:00    
🔄 Managing WireGuard service on root@*************...
Warning: Permanently added '*************' (ED25519) to the list of known hosts.
🔄 Stopping existing tangled0 interface...
bash: -c: option requires an argument
[#] ip link delete dev tangled0
🚀 Starting tangled0 interface...
[#] ip link add tangled0 type wireguard
[#] wg setconf tangled0 /dev/fd/63
[#] ip -4 address add ************/24 dev tangled0
[#] ip link set mtu 1420 up dev tangled0
✅ tangled0 interface is active
interface: tangled0
  public key: 4H+Eb8/HEE+HmjNUj9sE35+b8vd6jPwmYWRVttQYk28=
  private key: (hidden)
  listening port: 51820

peer: 33Rmv8S3LN8hrOS4ApR4d1B4QE/rfI5u1KOGUWAHOUg=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: GvyYVmo54BIzjxdaGWlkilqC1WCjB51Vqm1jWbURrzI=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: 7hlhxQJEFwIJXf66z9+RCfkxgR5JElRxZzUAEpVwogU=
  endpoint: *************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: oBuVapyIpIrkGscqfHzAv2E7pmKgu5mTbpJfS/l6b3A=
  endpoint: ***********:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: HAiRt1rsFxY/Ndf83ilX0SF1UiwKiSSkYm7F3UCj3RE=
  endpoint: ***********:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: fCiR/3ZK0JDpet8ZE0GcDdloS8pGCi7uNoB+HjJHCAc=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: mQrfJs/9ylhbLpEmodaYV7lrribYnslm7gO+jEkqDXw=
  endpoint: ***************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds
🔧 Enabling systemd service...
✅ WireGuard interface tangled0 is active on root@*************
✅ Successfully deployed aurora

[2/8] 🎯 Deploying dns-master to root@**************...
📋 Checking WireGuard installation on root@**************...
📦 Installing WireGuard on root@**************...
 ssh: connect to host ************** port 22: Operation timed out
❌ Failed to deploy dns-master: failed to ensure WireGuard installation: WireGuard installation failed: exit status 255
Continue with remaining deployments? (y/N): y

[3/8] 🎯 Deploying slave-uk to root@**************...
📋 Checking WireGuard installation on root@**************...
✅ WireGuard already installed on root@**************
📤 Uploading configuration to root@**************...
Warning: Permanently added '**************' (ED25519) to the list of known hosts.
tangled0.conf            100% 1166     5.5KB/s   00:00    
🔄 Managing WireGuard service on root@**************...
Warning: Permanently added '**************' (ED25519) to the list of known hosts.
bash: -c: option requires an argument
🔄 Stopping existing tangled0 interface...
[#] ip link delete dev tangled0
🚀 Starting tangled0 interface...
[#] ip link add tangled0 type wireguard
[#] wg setconf tangled0 /dev/fd/63
[#] ip -4 address add ************/24 dev tangled0
[#] ip link set mtu 1420 up dev tangled0
✅ tangled0 interface is active
interface: tangled0
  public key: GvyYVmo54BIzjxdaGWlkilqC1WCjB51Vqm1jWbURrzI=
  private key: (hidden)
  listening port: 51820

peer: 4H+Eb8/HEE+HmjNUj9sE35+b8vd6jPwmYWRVttQYk28=
  endpoint: *************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: 33Rmv8S3LN8hrOS4ApR4d1B4QE/rfI5u1KOGUWAHOUg=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: 7hlhxQJEFwIJXf66z9+RCfkxgR5JElRxZzUAEpVwogU=
  endpoint: *************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: oBuVapyIpIrkGscqfHzAv2E7pmKgu5mTbpJfS/l6b3A=
  endpoint: ***********:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: HAiRt1rsFxY/Ndf83ilX0SF1UiwKiSSkYm7F3UCj3RE=
  endpoint: ***********:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: fCiR/3ZK0JDpet8ZE0GcDdloS8pGCi7uNoB+HjJHCAc=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: mQrfJs/9ylhbLpEmodaYV7lrribYnslm7gO+jEkqDXw=
  endpoint: ***************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds
🔧 Enabling systemd service...
✅ WireGuard interface tangled0 is active on root@**************
✅ Successfully deployed slave-uk

[4/8] 🎯 Deploying slave-hk to root@*************...
📋 Checking WireGuard installation on root@*************...
✅ WireGuard already installed on root@*************
📤 Uploading configuration to root@*************...
Warning: Permanently added '*************' (ED25519) to the list of known hosts.
tangled0.conf            100% 1167    16.5KB/s   00:00    
🔄 Managing WireGuard service on root@*************...
Warning: Permanently added '*************' (ED25519) to the list of known hosts.
bash: -c: option requires an argument
🔄 Stopping existing tangled0 interface...
[#] ip link delete dev tangled0
🚀 Starting tangled0 interface...
[#] ip link add tangled0 type wireguard
[#] wg setconf tangled0 /dev/fd/63
[#] ip -4 address add ************/24 dev tangled0
[#] ip link set mtu 1420 up dev tangled0
✅ tangled0 interface is active
interface: tangled0
  public key: 7hlhxQJEFwIJXf66z9+RCfkxgR5JElRxZzUAEpVwogU=
  private key: (hidden)
  listening port: 51820

peer: 4H+Eb8/HEE+HmjNUj9sE35+b8vd6jPwmYWRVttQYk28=
  endpoint: *************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: 33Rmv8S3LN8hrOS4ApR4d1B4QE/rfI5u1KOGUWAHOUg=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: GvyYVmo54BIzjxdaGWlkilqC1WCjB51Vqm1jWbURrzI=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: oBuVapyIpIrkGscqfHzAv2E7pmKgu5mTbpJfS/l6b3A=
  endpoint: ***********:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: HAiRt1rsFxY/Ndf83ilX0SF1UiwKiSSkYm7F3UCj3RE=
  endpoint: ***********:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: fCiR/3ZK0JDpet8ZE0GcDdloS8pGCi7uNoB+HjJHCAc=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: mQrfJs/9ylhbLpEmodaYV7lrribYnslm7gO+jEkqDXw=
  endpoint: ***************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds
🔧 Enabling systemd service...
✅ WireGuard interface tangled0 is active on root@*************
✅ Successfully deployed slave-hk

[5/8] 🎯 Deploying slave-fremont to root@***********...
📋 Checking WireGuard installation on root@***********...
✅ WireGuard already installed on root@***********
📤 Uploading configuration to root@***********...
Warning: Permanently added '***********' (ED25519) to the list of known hosts.
tangled0.conf            100% 1169     5.2KB/s   00:00    
🔄 Managing WireGuard service on root@***********...
Warning: Permanently added '***********' (ED25519) to the list of known hosts.
bash: -c: option requires an argument
🔄 Stopping existing tangled0 interface...
[#] ip link delete dev tangled0
🚀 Starting tangled0 interface...
[#] ip link add tangled0 type wireguard
[#] wg setconf tangled0 /dev/fd/63
[#] ip -4 address add ************/24 dev tangled0
[#] ip link set mtu 1420 up dev tangled0
✅ tangled0 interface is active
interface: tangled0
  public key: oBuVapyIpIrkGscqfHzAv2E7pmKgu5mTbpJfS/l6b3A=
  private key: (hidden)
  listening port: 51820

peer: 4H+Eb8/HEE+HmjNUj9sE35+b8vd6jPwmYWRVttQYk28=
  endpoint: *************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: 33Rmv8S3LN8hrOS4ApR4d1B4QE/rfI5u1KOGUWAHOUg=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: GvyYVmo54BIzjxdaGWlkilqC1WCjB51Vqm1jWbURrzI=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: 7hlhxQJEFwIJXf66z9+RCfkxgR5JElRxZzUAEpVwogU=
  endpoint: *************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: HAiRt1rsFxY/Ndf83ilX0SF1UiwKiSSkYm7F3UCj3RE=
  endpoint: ***********:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: fCiR/3ZK0JDpet8ZE0GcDdloS8pGCi7uNoB+HjJHCAc=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: mQrfJs/9ylhbLpEmodaYV7lrribYnslm7gO+jEkqDXw=
  endpoint: ***************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds
🔧 Enabling systemd service...
✅ WireGuard interface tangled0 is active on root@***********
✅ Successfully deployed slave-fremont

[6/8] 🎯 Deploying slave-toronto to root@***********...
📋 Checking WireGuard installation on root@***********...
✅ WireGuard already installed on root@***********
📤 Uploading configuration to root@***********...
Warning: Permanently added '***********' (ED25519) to the list of known hosts.
tangled0.conf            100% 1169     4.4KB/s   00:00    
🔄 Managing WireGuard service on root@***********...
Warning: Permanently added '***********' (ED25519) to the list of known hosts.
bash: -c: option requires an argument
🔄 Stopping existing tangled0 interface...
[#] ip link delete dev tangled0
🚀 Starting tangled0 interface...
[#] ip link add tangled0 type wireguard
[#] wg setconf tangled0 /dev/fd/63
[#] ip -4 address add ************/24 dev tangled0
[#] ip link set mtu 1420 up dev tangled0
✅ tangled0 interface is active
interface: tangled0
  public key: HAiRt1rsFxY/Ndf83ilX0SF1UiwKiSSkYm7F3UCj3RE=
  private key: (hidden)
  listening port: 51820

peer: 4H+Eb8/HEE+HmjNUj9sE35+b8vd6jPwmYWRVttQYk28=
  endpoint: *************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: 33Rmv8S3LN8hrOS4ApR4d1B4QE/rfI5u1KOGUWAHOUg=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: GvyYVmo54BIzjxdaGWlkilqC1WCjB51Vqm1jWbURrzI=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: 7hlhxQJEFwIJXf66z9+RCfkxgR5JElRxZzUAEpVwogU=
  endpoint: *************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: oBuVapyIpIrkGscqfHzAv2E7pmKgu5mTbpJfS/l6b3A=
  endpoint: ***********:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: fCiR/3ZK0JDpet8ZE0GcDdloS8pGCi7uNoB+HjJHCAc=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: mQrfJs/9ylhbLpEmodaYV7lrribYnslm7gO+jEkqDXw=
  endpoint: ***************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds
🔧 Enabling systemd service...
✅ WireGuard interface tangled0 is active on root@***********
✅ Successfully deployed slave-toronto

[7/8] 💻 Setting up mx (MacBook)...
📋 MacBook config ready at: output/mx/tangled0.conf
💡 To activate on your MacBook:
   sudo cp output/mx/tangled0.conf /etc/wireguard/
   sudo wg-quick up tangled0

[8/8] 🎯 Deploying slave-swiss to root@***************...
📋 Checking WireGuard installation on root@***************...
✅ WireGuard already installed on root@***************
📤 Uploading configuration to root@***************...
Warning: Permanently added '***************' (ED25519) to the list of known hosts.
tangled0.conf            100% 1165     4.2KB/s   00:00    
🔄 Managing WireGuard service on root@***************...
Warning: Permanently added '***************' (ED25519) to the list of known hosts.
bash: -c: option requires an argument
🔄 Stopping existing tangled0 interface...
[#] ip link delete dev tangled0
🚀 Starting tangled0 interface...
[#] ip link add tangled0 type wireguard
[#] wg setconf tangled0 /dev/fd/63
[#] ip -4 address add ************/24 dev tangled0
[#] ip link set mtu 1420 up dev tangled0
✅ tangled0 interface is active
interface: tangled0
  public key: mQrfJs/9ylhbLpEmodaYV7lrribYnslm7gO+jEkqDXw=
  private key: (hidden)
  listening port: 51820

peer: 4H+Eb8/HEE+HmjNUj9sE35+b8vd6jPwmYWRVttQYk28=
  endpoint: *************:51820
  allowed ips: ************/32
  latest handshake: Now
  transfer: 92 B received, 180 B sent
  persistent keepalive: every 15 seconds

peer: 33Rmv8S3LN8hrOS4ApR4d1B4QE/rfI5u1KOGUWAHOUg=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: GvyYVmo54BIzjxdaGWlkilqC1WCjB51Vqm1jWbURrzI=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: 7hlhxQJEFwIJXf66z9+RCfkxgR5JElRxZzUAEpVwogU=
  endpoint: *************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: oBuVapyIpIrkGscqfHzAv2E7pmKgu5mTbpJfS/l6b3A=
  endpoint: ***********:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: HAiRt1rsFxY/Ndf83ilX0SF1UiwKiSSkYm7F3UCj3RE=
  endpoint: ***********:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds

peer: fCiR/3ZK0JDpet8ZE0GcDdloS8pGCi7uNoB+HjJHCAc=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 148 B sent
  persistent keepalive: every 15 seconds
🔧 Enabling systemd service...
✅ WireGuard interface tangled0 is active on root@***************
✅ Successfully deployed slave-swiss

==================================================
📊 DEPLOYMENT SUMMARY
==================================================
✅ Successfully deployed (7): [aurora slave-uk slave-hk slave-fremont slave-toronto mx slave-swiss]
❌ Failed (1): [dns-master]
MacBook-Pro:tangled marcxavier$ wg-quick up ./output/mx/tangled0.conf
wg-quick must be run as root. Please enter the password for marcxavier to continue: 
[#] wireguard-go utun
[+] Interface for tangled0 is utun6
[#] wg setconf utun6 /dev/fd/63
[#] ifconfig utun6 inet ************/24 ************ alias
[#] ifconfig utun6 up
[#] route -q -n add -inet ************/32 -interface utun6
[#] route -q -n add -inet ************/32 -interface utun6
[#] route -q -n add -inet ************/32 -interface utun6
[#] route -q -n add -inet ************/32 -interface utun6
[#] route -q -n add -inet ************/32 -interface utun6
[#] route -q -n add -inet ************/32 -interface utun6
[#] route -q -n add -inet ************/32 -interface utun6
[+] Backgrounding route monitor
MacBook-Pro:tangled marcxavier$ sudo wg show utun6
interface: utun6
  public key: fCiR/3ZK0JDpet8ZE0GcDdloS8pGCi7uNoB+HjJHCAc=
  private key: (hidden)
  listening port: 51820

peer: mQrfJs/9ylhbLpEmodaYV7lrribYnslm7gO+jEkqDXw=
  endpoint: ***************:51820
  allowed ips: ************/32
  latest handshake: 17 seconds ago
  transfer: 92 B received, 212 B sent
  persistent keepalive: every 15 seconds

peer: HAiRt1rsFxY/Ndf83ilX0SF1UiwKiSSkYm7F3UCj3RE=
  endpoint: ***********:51820
  allowed ips: ************/32
  latest handshake: 17 seconds ago
  transfer: 92 B received, 212 B sent
  persistent keepalive: every 15 seconds

peer: oBuVapyIpIrkGscqfHzAv2E7pmKgu5mTbpJfS/l6b3A=
  endpoint: ***********:51820
  allowed ips: ************/32
  latest handshake: 17 seconds ago
  transfer: 92 B received, 212 B sent
  persistent keepalive: every 15 seconds

peer: GvyYVmo54BIzjxdaGWlkilqC1WCjB51Vqm1jWbURrzI=
  endpoint: **************:51820
  allowed ips: ************/32
  latest handshake: 17 seconds ago
  transfer: 92 B received, 212 B sent
  persistent keepalive: every 15 seconds

peer: 4H+Eb8/HEE+HmjNUj9sE35+b8vd6jPwmYWRVttQYk28=
  endpoint: *************:51820
  allowed ips: ************/32
  latest handshake: 17 seconds ago
  transfer: 92 B received, 212 B sent
  persistent keepalive: every 15 seconds

peer: 7hlhxQJEFwIJXf66z9+RCfkxgR5JElRxZzUAEpVwogU=
  endpoint: *************:51820
  allowed ips: ************/32
  latest handshake: 17 seconds ago
  transfer: 92 B received, 212 B sent
  persistent keepalive: every 15 seconds

peer: 33Rmv8S3LN8hrOS4ApR4d1B4QE/rfI5u1KOGUWAHOUg=
  endpoint: **************:51820
  allowed ips: ************/32
  transfer: 0 B received, 592 B sent
  persistent keepalive: every 15 seconds
  