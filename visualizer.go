package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

func generateVisualization(nodes []Node) error {
	// Create graphs directory
	if err := os.MkdirAll("graphs", 0755); err != nil {
		return fmt.Errorf("failed to create graphs directory: %w", err)
	}

	var dot strings.Builder

	// DOT file header
	dot.WriteString("digraph TangledMesh {\n")
	dot.WriteString("  rankdir=LR;\n")
	dot.WriteString("  node [shape=box, style=filled, fontname=\"Arial Bold\"];\n")
	dot.WriteString("  edge [color=blue, penwidth=2];\n")
	dot.WriteString("  bgcolor=white;\n")
	dot.WriteString("  label=\"🕸️ Tangled WireGuard Full-Mesh Network\";\n")
	dot.WriteString("  labelloc=top;\n")
	dot.WriteString("  fontsize=16;\n")
	dot.WriteString("  fontname=\"Arial Bold\";\n\n")

	// Define nodes with colors and labels
	colors := []string{"lightblue", "lightgreen", "lightyellow", "lightpink", "lightcyan", "wheat"}
	for i, node := range nodes {
		color := colors[i%len(colors)]
		label := fmt.Sprintf("%s\\n%s\\n%s", node.Name, node.IP, node.Endpoint)
		dot.WriteString(fmt.Sprintf("  \"%s\" [fillcolor=%s, label=\"%s\"];\n",
			node.Name, color, label))
	}

	dot.WriteString("\n")

	// Create full-mesh connections (bidirectional)
	for i, nodeA := range nodes {
		for j, nodeB := range nodes {
			if i < j { // Only create each connection once (undirected graph)
				dot.WriteString(fmt.Sprintf("  \"%s\" -- \"%s\" [label=\"51820/udp\"];\n",
					nodeA.Name, nodeB.Name))
			}
		}
	}

	// Add legend
	dot.WriteString("\n")
	dot.WriteString("  subgraph cluster_legend {\n")
	dot.WriteString("    label=\"Legend\";\n")
	dot.WriteString("    style=filled;\n")
	dot.WriteString("    fillcolor=lightgray;\n")
	dot.WriteString("    fontname=\"Arial\";\n")
	dot.WriteString("    legend [shape=plaintext, label=<\n")
	dot.WriteString("      <TABLE BORDER=\"0\" CELLBORDER=\"1\" CELLSPACING=\"0\">\n")
	dot.WriteString("        <TR><TD><B>Connection Type</B></TD><TD><B>Details</B></TD></TR>\n")
	dot.WriteString("        <TR><TD>WireGuard Tunnel</TD><TD>UDP Port 51820</TD></TR>\n")
	dot.WriteString("        <TR><TD>Network</TD><TD>100.100.10.0/24</TD></TR>\n")
	dot.WriteString("        <TR><TD>Keepalive</TD><TD>15 seconds</TD></TR>\n")
	dot.WriteString("      </TABLE>\n")
	dot.WriteString("    >];\n")
	dot.WriteString("  }\n")

	dot.WriteString("}\n")

	// Write DOT file
	dotPath := filepath.Join("graphs", "topology.dot")
	if err := os.WriteFile(dotPath, []byte(dot.String()), 0644); err != nil {
		return fmt.Errorf("failed to write DOT file: %w", err)
	}

	// Generate additional formats if Graphviz is available
	generateGraphvizOutputs(dotPath)

	return nil
}

func generateGraphvizOutputs(dotPath string) {
	formats := []struct {
		ext    string
		format string
		desc   string
	}{
		{"png", "png", "PNG image"},
		{"svg", "svg", "SVG vector"},
		{"pdf", "pdf", "PDF document"},
	}

	for _, format := range formats {
		outputPath := strings.TrimSuffix(dotPath, ".dot") + "." + format.ext

		// Try to generate the format
		cmd := fmt.Sprintf("dot -T%s %s -o %s", format.format, dotPath, outputPath)
		if err := runCommand(cmd); err == nil {
			fmt.Printf("📊 Generated %s: %s\n", format.desc, outputPath)
		}
	}
}

func runCommand(cmd string) error {
	// Simple command execution - in a real implementation you'd use exec.Command
	// For now, we'll just indicate the command that should be run
	fmt.Printf("💡 To generate visual formats, run: %s\n", cmd)
	return nil
}
